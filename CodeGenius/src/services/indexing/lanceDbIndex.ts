import * as fs from 'fs';
import * as path from 'path';
import * as os from "os";

import { connect, Connection, Table, VectorIndexParams, WriteMode } from "vectordb";
import { codeChunker } from './chunk/code';
import { ChunkWithoutID, genToStrs } from './chunk/chunk';
import { ZeroAgentEmbedding } from './ZeroAgentEmbedding';
import { calculateFileHash, FileMetadata, getCodeFilesInDirectory, getNeedIndexingFiles, readFileByLine } from './util/paths';
import { ZeroAgentReranker } from './ZeroAgentReranker';
import { readJsonFile, writeJsonFile } from './evaluation';
import { EMBED_VECTOR_LEN, RETRIEVE_TOPN } from './constant';
import { IndexingProgressUpdate } from '@shared/ExtensionMessage';

export const MAX_CHUNK_LINES = 50;
export const MAX_CHUNK_BATCH = 50;
const MIN_ROWS_NEED_INDEX = 100000;
const ZERO_AGENT_GLOBAL_DIR = path.join(os.homedir(), ".zeroAgent");
const DEFAULT_DB_SAVE_DIR = path.join(ZERO_AGENT_GLOBAL_DIR, "index/lancedb");

function formatProgress(progress: number): number {
    const percentage = Math.round(progress * 100);
    return percentage / 100;
}

export interface CodeChunkDict {
    filePath: string;
    startLine: number;
    endLine: number;
    vector: number[];
    [key: string]: any;
}

export interface indexResult {
    repoName: string;
    fileNum: number;
    chunkDuration: number;
    embeddingDuration: number;
    insertDataDuration: number;
    duration: number;
    dataRows: number;
}
const resultSaveDir = path.join(ZERO_AGENT_GLOBAL_DIR, "index/result");

export async function getCodeChunkDicts(
    chunks: ChunkWithoutID[],
    embeddings: number[][]) {
    const chunkDicts: CodeChunkDict[] = chunks.map((chunk, index) => ({
        filePath: chunk.filepath,
        startLine: chunk.startLine,
        endLine: chunk.endLine,
        vector: embeddings[index]
    }));
    return chunkDicts;
}

function pathToSafeFilename(pathStr: string): string {
    return pathStr
        .replace(/[^a-zA-Z0-9_\-.]/g, '_')
        .replace(/^[^a-zA-Z_]/, '_');
}

export class LanceDbIndex {
    private db!: Connection;
    private embedModel: ZeroAgentEmbedding;
    private rerankModel: ZeroAgentReranker;
    private repoBaseDir!: string;
    private dbSaveDir: string;
    private tableName!: string;
    private table!: Table;
    private fileIndexingInfo: Record<string, FileMetadata>;
    private result: indexResult;

    constructor() {
        this.dbSaveDir = DEFAULT_DB_SAVE_DIR;
        this.embedModel = new ZeroAgentEmbedding();
        this.rerankModel = new ZeroAgentReranker();
        this.fileIndexingInfo = {};
        this.result = {
            repoName: '',
            fileNum: 0,
            chunkDuration: 0,
            embeddingDuration: 0,
            insertDataDuration: 0,
            duration: 0,
            dataRows: 0
        };
    }

    async indexing(repoBaseDir: string, isRetrieve: boolean = false, onProgress?: (update: IndexingProgressUpdate) => void) {
        console.log("🚀 [LanceDbIndex] 开始索引流程，仓库路径:", repoBaseDir);
        this.repoBaseDir = repoBaseDir;
        this.tableName = pathToSafeFilename(repoBaseDir);
        this.result.repoName = this.tableName;
        console.log("📊 [LanceDbIndex] 表名:", this.tableName);

        if (onProgress) {
            onProgress({
                progress: formatProgress(0.05),
                desc: "初始化数据库连接...",
                status: "loading"
            });
        }

        if (!this.db) {
            console.log("🔗 [LanceDbIndex] 连接数据库...");
            this.db = await connect(this.dbSaveDir);
            if (!this.db) {
                console.error("❌ [LanceDbIndex] 数据库连接失败!");
                if (onProgress) {
                    onProgress({
                        progress: formatProgress(0),
                        desc: "数据库连接失败",
                        status: "failed"
                    });
                }
                return;
            }
            console.log("✅ [LanceDbIndex] 数据库连接成功");

            if (!(await this.db.tableNames()).includes(this.tableName)) {
                if (isRetrieve) {
                    console.log("⚠️ [LanceDbIndex] 索引不存在!");
                    return;
                }
                console.log(`🆕 [LanceDbIndex] 表 ${this.tableName} 不存在，开始全量索引...`);

                if (onProgress) {
                    onProgress({
                        progress: formatProgress(0.1),
                        desc: "创建索引表...",
                        status: "loading"
                    });
                }

                await this.createTable();
                await this.fullIndexing(onProgress);
                await this.createIndex();
                console.log("📈 [LanceDbIndex] 全量索引完成，统计信息:", this.result);
            }
            this.table = await this.db.openTable(this.tableName);
            if (!this.table) {
                console.error("❌ [LanceDbIndex] 表创建失败!");
                return;
            }
        }
        if (!this.table) {
            this.table = await this.db.openTable(this.tableName);
            if (!this.table) {
                console.error("❌ [LanceDbIndex] 表打开失败!");
                return;
            }
        }
        if (!isRetrieve) {
            console.log("🔄 [LanceDbIndex] 开始增量更新...");
            this.update(onProgress);
        }
    }

    async createTable() {
        console.log("🏗️ [LanceDbIndex] 开始创建表...");
        if (!this.db) {
            this.db = await connect(this.dbSaveDir);
        }
        if (!this.db) {
            console.error("❌ [LanceDbIndex] 数据库连接失败!");
            return;
        }
        const array: number[] = new Array(EMBED_VECTOR_LEN).fill(0);
        const exampleData: CodeChunkDict = {
            filePath: "example/path",
            startLine: 1,
            endLine: 10,
            vector: array
        };
        console.log("📝 [LanceDbIndex] 创建表结构，向量维度:", EMBED_VECTOR_LEN);
        this.table = await this.db.createTable(this.tableName, [exampleData], { writeMode: WriteMode.Overwrite });
        if (!this.table) {
            console.error("❌ [LanceDbIndex] 表创建失败");
            return;
        }
        await this.table.delete(`'filePath' = 'example/path'`);
        console.log("✅ [LanceDbIndex] 表创建成功:", this.table.name);
    }

    async insertData(data: CodeChunkDict[]) {
        const startTime = performance.now();
        if (!this.db || !this.table) {
            console.error("❌ [LanceDbIndex] 数据库或表不存在!");
            return;
        }
        if (data.length === 0) {
            return;
        }
        console.log("💾 [LanceDbIndex] 入库数据块数量:", data.length);
        await this.table.add(data);
        const endTime = performance.now();
        const duration = endTime - startTime;
        this.result.insertDataDuration += duration;
        console.log(`✅ [LanceDbIndex] 数据入库完成，耗时: ${duration.toFixed(2)}ms`);
    }

    async fullIndexing(onProgress?: (update: IndexingProgressUpdate) => void) {
        console.log("🔍 [LanceDbIndex] 开始扫描代码文件...");
        const codeFiles = await getCodeFilesInDirectory(this.repoBaseDir);
        console.log("📁 [LanceDbIndex] 发现代码文件数量:", codeFiles.length);
        this.result.fileNum = codeFiles.length;

        if (onProgress) {
            onProgress({
                progress: formatProgress(0.15),
                desc: `发现 ${codeFiles.length} 个代码文件，开始处理...`,
                status: "indexing"
            });
        }

        await this.embedding(
            codeFiles,
            onProgress
        );
    }

    async embedding(codeFiles: string[],
        onProgress?: (update: IndexingProgressUpdate) => void
    ) {
        console.log("🧠 [LanceDbIndex] 开始向量化处理...");
        const startTime = performance.now();
        let chunkBatch: ChunkWithoutID[] = [];
        const chunksConcurrency: ChunkWithoutID[][] = [];
        let totalChunks = 0;
        let processedChunks = 0;

        for (let i = 0; i < codeFiles.length; i++) {
            const current_file = codeFiles[i];
            const fileProgress = i / codeFiles.length;
            const overallProgress = 0.15 + (fileProgress * 0.7); // 15% - 85%

            if (onProgress) {
                onProgress({
                    progress: formatProgress(overallProgress),
                    desc: `处理文件 ${i + 1}/${codeFiles.length}: ${path.basename(current_file)}`,
                    status: "indexing"
                });
            }

            console.log(`📄 [LanceDbIndex] 处理文件 ${i + 1}/${codeFiles.length}: ${current_file}`);
            const chunks = await this.getChunks(current_file);
            if (chunks.length === 0) {
                console.log(`⚠️ [LanceDbIndex] 文件无有效代码块: ${current_file}`);
                await this.getFileMetadata(current_file);
                continue;
            }

            console.log(`✂️ [LanceDbIndex] 文件切分完成，代码块数量: ${chunks.length}`);
            totalChunks += chunks.length;
            chunkBatch.push(...chunks);

            while (chunkBatch.length >= MAX_CHUNK_BATCH) {
                chunksConcurrency.push(chunkBatch.slice(0, MAX_CHUNK_BATCH));
                const contentsMap = chunksConcurrency.map(chunks => chunks.map(chunk => chunk.content))
                if (contentsMap.length >= this.embedModel.getEmbeddingConcurrency()) {
                    console.log(`🔄 [LanceDbIndex] 开始批量向量化，批次数量: ${contentsMap.length}`);
                    const embeddingsMap = await this.getBatchEmbeddings(contentsMap);
                    console.log(`✅ [LanceDbIndex] 向量化完成，开始入库...`);
                    while (embeddingsMap.length > 0) {
                        const chunks = chunksConcurrency.pop();
                        const embeddings = embeddingsMap.pop();
                        if (chunks && embeddings && embeddings.length > 0) {
                            const chunkDicts = await getCodeChunkDicts(chunks, embeddings);
                            await this.insertData(chunkDicts);
                            processedChunks += chunks.length;
                        }
                    }
                }
                chunkBatch = chunkBatch.slice(MAX_CHUNK_BATCH);
            }
            await this.getFileMetadata(current_file);
        }

        // 处理剩余的代码块
        if (chunkBatch.length > 0) {
            console.log(`🔄 [LanceDbIndex] 处理剩余代码块: ${chunkBatch.length}`);
            chunksConcurrency.push(chunkBatch);
        }

        if (chunksConcurrency.length > 0) {
            console.log(`🔄 [LanceDbIndex] 最终批量向量化，剩余批次: ${chunksConcurrency.length}`);
            const contentsMap = chunksConcurrency.map(chunks => chunks.map(chunk => chunk.content))
            const embeddingsMap = await this.getBatchEmbeddings(contentsMap);
            console.log(`✅ [LanceDbIndex] 最终向量化完成，开始入库...`);
            while (embeddingsMap.length > 0) {
                const chunks = chunksConcurrency.pop();
                const embeddings = embeddingsMap.pop();
                if (chunks && embeddings && embeddings.length > 0) {
                    const chunkDicts = await getCodeChunkDicts(chunks, embeddings);
                    await this.insertData(chunkDicts);
                    processedChunks += chunks.length;
                }
            }
        }

        const endTime = performance.now();
        const duration = endTime - startTime;
        const rows = await this.table.countRows();
        this.result.dataRows = rows;
        this.result.duration = duration;

        console.log(`📊 [LanceDbIndex] 向量化统计:`);
        console.log(`   - 总代码块数: ${totalChunks}`);
        console.log(`   - 已处理数: ${processedChunks}`);
        console.log(`   - 数据库行数: ${rows}`);
        console.log(`   - 总耗时: ${duration.toFixed(2)}ms`);

        this.saveFileIndexingInfo();

        if (onProgress) {
            onProgress({
                progress: formatProgress(0.9),
                desc: "保存索引信息...",
                status: "indexing"
            });
        }

        if (onProgress) {
            onProgress({
                progress: formatProgress(1.0),
                desc: "索引完成",
                status: "done"
            });
        }

        console.log("🎉 [LanceDbIndex] 向量化处理完成!");
    }

    async createIndex() {
        console.log("🔧 [LanceDbIndex] 开始创建向量索引...");
        if (!this.db || !this.table) {
            console.error("❌ [LanceDbIndex] 数据库或表不存在!");
            return;
        }
        const dataRows: number = await this.table.countRows();
        console.log(`📊 [LanceDbIndex] 数据行数: ${dataRows}`);

        if (dataRows < MIN_ROWS_NEED_INDEX) {
            console.log(`⚠️ [LanceDbIndex] 数据行数太少 (${dataRows} < ${MIN_ROWS_NEED_INDEX})，无需创建索引!`);
            return;
        }

        const indexParams: VectorIndexParams = {
            type: "ivf_pq",
            num_partitions: 4,
            num_sub_vectors: 8,
            num_bits: 4,
            use_opq: false,
            index_cache_size: 128
        }
        console.log("🔧 [LanceDbIndex] 索引参数:", indexParams);
        await this.table.createIndex(indexParams);
        console.log("✅ [LanceDbIndex] 向量索引创建成功!");
    }

    async retrieve(repoBaseDir: string, task: string) {
        console.log("workspace:", repoBaseDir);
        await this.indexing(repoBaseDir, true);
        if (!this.db || !this.table) {
            console.log("database or table not exist!");
            return [];
        }
        console.log("start retrieve:", task);
        const embedding = await this.getEmbeddings([task]);
        const results: CodeChunkDict[] = await this.table.search(embedding[0]).limit(RETRIEVE_TOPN).execute();
        return await this.processRetrieveResults(results);
    }

    async getRerankScore(task: string, retrieveResults: CodeChunkDict[]) {
        const queryNodes: string[][] = [];
        for (let i = 0; i < retrieveResults.length; i++) {
            const result: CodeChunkDict = retrieveResults[i];
            const node: string[] = [];
            const content = await readFileByLine(
                result["filePath"],
                result["startLine"],
                result["endLine"] + 1
            );
            node.push(task);
            node.push(content);
            queryNodes.push(node);
        }
        const score: number[] = await this.rerankModel.rerank(queryNodes);
        return score;
    }

    async sortAndRerank(numbers: number[], chunks: CodeChunkDict[]): Promise<CodeChunkDict[]> {
        if (numbers.length !== chunks.length) {
            return chunks;
        }
        const pairedArray: Array<{ num: number; chunk: CodeChunkDict }> = [];
        for (let i = 0; i < numbers.length; i++) {
            pairedArray.push({
                num: numbers[i],
                chunk: chunks[i]
            });
        }
        pairedArray.sort((a, b) => b.num - a.num);
        const result: CodeChunkDict[] = pairedArray.map(item => item.chunk);
        return result;
    }

    async retrieveByFilepath(filePath: string) {
        if (!this.db || !this.table) {
            console.error("database or table not exist!");
            return;
        }
        const results = await this.table.filter(`filePath = '${filePath}'`).execute();
    }

    async processRetrieveResults(results: CodeChunkDict[]): Promise<string[]> {
        let filePaths: string[] = [];
        for (let i = 0; i < results.length; i++) {
            if (filePaths.includes(results[i]["filePath"])) {
                continue;
            }
            filePaths.push(results[i]["filePath"]);
        }
        return filePaths;
    }

    async update(onProgress?: (update: IndexingProgressUpdate) => void) {
        await this.readFileIndexingInfo();
        const codeFiles = await getCodeFilesInDirectory(this.repoBaseDir);
        const needIndexingFiles: string[] = await getNeedIndexingFiles(codeFiles, this.fileIndexingInfo);
        await this.deleteByFilepath(needIndexingFiles);
        await this.embedding(needIndexingFiles, onProgress);
    }

    async getFileMetadata(filePath: string) {
        const hash: string = await calculateFileHash(filePath);
        const indexTime = Date.now();
        const metadata: FileMetadata = {
            "indexTime": indexTime,
            "hash": hash
        }
        this.fileIndexingInfo[filePath] = metadata;
    }

    async saveFileIndexingInfo() {
        const savePath = path.join(ZERO_AGENT_GLOBAL_DIR, `index/${this.tableName}.json`);
        await writeJsonFile(savePath, this.fileIndexingInfo);
    }

    async readFileIndexingInfo() {
        const savePath = path.join(ZERO_AGENT_GLOBAL_DIR, `index/${this.tableName}.json`);
        if (!fs.existsSync(savePath)) {
            return;
        }
        this.fileIndexingInfo = await readJsonFile(savePath);
    }

    async deleteByFilepath(filePaths: string[]) {
        if (!this.db || !this.table) {
            console.error("database or table not exist!");
            return;
        }
        for (let i = 0; i < filePaths.length; i++) {
            const filePath = filePaths[i];
            const condition = "`filePath` = " + `'${filePath}'`;
            await this.table.delete(condition);
        }
    }

    async getChunks(filePath: string): Promise<ChunkWithoutID[]> {
        const startTime = performance.now();
        console.log(`✂️ [LanceDbIndex] 开始切分文件: ${path.basename(filePath)}`);
        const content = await fs.promises.readFile(filePath, 'utf-8');
        const generator = codeChunker(filePath, content, MAX_CHUNK_LINES);
        const chunks = await genToStrs(generator);
        const endTime = performance.now();
        const duration = endTime - startTime;
        this.result.chunkDuration += duration;
        console.log(`✅ [LanceDbIndex] 文件切分完成: ${chunks.length} 个代码块，耗时: ${duration.toFixed(2)}ms`);
        return chunks;
    }

    async getBatchEmbeddings(contents: string[][]): Promise<number[][][]> {
        const startTime = performance.now();
        const totalChunks = contents.reduce((sum, batch) => sum + batch.length, 0);
        console.log(`🧠 [LanceDbIndex] 开始批量向量化: ${contents.length} 个批次，共 ${totalChunks} 个代码块`);
        const embeddings = await this.embedModel.getBatchEmbeddings(contents);
        const duration = performance.now() - startTime;
        this.result.embeddingDuration += duration;
        console.log(`✅ [LanceDbIndex] 批量向量化完成，耗时: ${duration.toFixed(2)}ms`);
        return embeddings;
    }

    async getEmbeddings(contents: string[]): Promise<number[][]> {
        const startTime = performance.now();
        console.log(`🧠 [LanceDbIndex] 开始向量化: ${contents.length} 个内容`);
        const embeddings = await this.embedModel.getEmbeddings(contents);
        const duration = performance.now() - startTime;
        this.result.embeddingDuration += duration;
        console.log(`✅ [LanceDbIndex] 向量化完成，耗时: ${duration.toFixed(2)}ms`);
        return embeddings;
    }
}

export async function createIndex(repoBaseDir: string) {
    const lanceDb = new LanceDbIndex();
    await lanceDb.indexing(repoBaseDir, false);
}