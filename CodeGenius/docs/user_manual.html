<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>零号员工插件使用手册</title>
    <script src="script/marked.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .header h1 {
            font-size: 3em;
            margin-bottom: 10px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .header p {
            font-size: 1.2em;
            color: #666;
            margin-bottom: 20px;
        }

        .version-badge {
            display: inline-block;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9em;
            font-weight: 500;
        }

        .main-content {
            display: grid;
            grid-template-columns: 300px 1fr;
            gap: 30px;
            align-items: start;
        }

        .sidebar {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            position: sticky;
            top: 20px;
            max-height: calc(100vh - 40px);
            overflow-y: auto;
        }

        .sidebar h3 {
            color: #333;
            margin-bottom: 20px;
            font-size: 1.3em;
            text-align: center;
            padding-bottom: 15px;
            border-bottom: 2px solid #f0f0f0;
        }

        .toc {
            list-style: none;
        }

        .toc li {
            margin-bottom: 8px;
        }

        .toc a {
            text-decoration: none;
            color: #555;
            padding: 8px 12px;
            border-radius: 8px;
            display: block;
            transition: all 0.3s ease;
            font-size: 0.95em;
        }

        .toc a:hover {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            transform: translateX(5px);
        }

        .toc .level-1 {
            font-weight: 600;
            color: #333;
            margin-top: 15px;
        }

        .toc .level-2 {
            margin-left: 15px;
            font-weight: 500;
        }

        .toc .level-3 {
            margin-left: 30px;
            font-size: 0.9em;
        }

        .content {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .section {
            margin-bottom: 50px;
            opacity: 0;
            animation: fadeInUp 0.6s ease forwards;
        }

        .section:nth-child(odd) {
            animation-delay: 0.1s;
        }

        .section:nth-child(even) {
            animation-delay: 0.2s;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }

            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .section h2 {
            color: #333;
            font-size: 2.2em;
            margin-bottom: 25px;
            padding-bottom: 15px;
            border-bottom: 3px solid transparent;
            background: linear-gradient(90deg, #f0f0f0, #f0f0f0) no-repeat;
            background-size: 100% 3px;
            background-position: bottom;
            position: relative;
        }

        .section h2::before {
            content: '';
            position: absolute;
            bottom: -3px;
            left: 0;
            width: 60px;
            height: 3px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .section h3 {
            color: #444;
            font-size: 1.6em;
            margin: 30px 0 15px 0;
            padding-left: 15px;
            border-left: 4px solid #667eea;
        }

        .section h4 {
            color: #555;
            font-size: 1.3em;
            margin: 20px 0 10px 0;
            padding-left: 20px;
            position: relative;
        }

        .section h4::before {
            content: '▶';
            position: absolute;
            left: 0;
            color: #764ba2;
            font-size: 0.8em;
        }

        .section p {
            margin-bottom: 15px;
            color: #666;
            line-height: 1.8;
        }

        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }

        .feature-card {
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
            border-radius: 15px;
            padding: 25px;
            border: 1px solid rgba(102, 126, 234, 0.2);
            transition: all 0.3s ease;
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(102, 126, 234, 0.2);
        }

        .feature-card h4 {
            color: #333;
            margin-bottom: 10px;
            border: none;
            padding: 0;
        }

        .feature-card h4::before {
            content: '🔧';
            margin-right: 8px;
        }

        .step-list {
            background: rgba(102, 126, 234, 0.05);
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
        }

        .step-item {
            display: flex;
            align-items: flex-start;
            margin-bottom: 20px;
            padding: 15px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        }

        .step-number {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 15px;
            flex-shrink: 0;
        }

        .tip-box {
            background: linear-gradient(135deg, rgba(255, 193, 7, 0.1), rgba(255, 152, 0, 0.1));
            border-left: 4px solid #ffc107;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }

        .tip-box::before {
            content: '💡 ';
            font-size: 1.2em;
        }

        .warning-box {
            background: linear-gradient(135deg, rgba(220, 53, 69, 0.1), rgba(255, 87, 51, 0.1));
            border-left: 4px solid #dc3545;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }

        .warning-box::before {
            content: '⚠️ ';
            font-size: 1.2em;
        }

        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 10px;
            font-family: 'Consolas', 'Monaco', monospace;
            margin: 15px 0;
            overflow-x: auto;
        }

        .img-code-block {
            background: rgba(255, 193, 7, 0.1);
            color: #333;
            padding: 20px;
            border-radius: 10px;
            font-family: 'Monaco', 'Monaco', monospace;
            margin: 15px 0;
            overflow-x: auto;
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }

            .sidebar {
                position: static;
                max-height: none;
            }

            .header h1 {
                font-size: 2.2em;
            }

            .section h2 {
                font-size: 1.8em;
            }
        }

        .scroll-to-top {
            position: fixed;
            bottom: 30px;
            right: 30px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
            transition: all 0.3s ease;
            opacity: 0;
        }

        .scroll-to-top.visible {
            opacity: 1;
        }

        .scroll-to-top:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 35px rgba(102, 126, 234, 0.4);
        }

        img {
            max-width: 720px;
        }

        table {
            border: 1px solid #e0e0e0;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        th {
            background: linear-gradient(#f5f7fa, #e6e9ef);

        }

        td {
            transition: background 0.3s ease;
            /* 悬停动画 */
        }

        tr:hover td {
            background-color: #f8f9fa;
        }

        caption {
            caption-side: bottom;
            /* 标题在表格下方 */
            font-style: italic;
            color: #666;
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="header">
            <h1>零号员工插件使用手册</h1>
            <p>全面的功能指南与最佳实践</p>
            <span class="version-badge">V1.0.0</span>
        </div>

        <div class="main-content">
            <div class="sidebar">
                <h3>📋 目录导航</h3>
                <ul class="toc">
                    <li><a href="#section-1" class="level-1">一、安装配置</a></li>
                    <li><a href="#section-1-1" class="level-2">01. 前置条件</a></li>
                    <li><a href="#section-1-2" class="level-2">02. 安装步骤</a></li>
                    <li><a href="#section-1-3" class="level-2">03. 插件自动更新设置</a></li>
                    <li><a href="#section-1-4" class="level-2">04. Remote模式安装说明</a></li>

                    <li><a href="#section-2" class="level-1">二、基础配置</a></li>
                    <li><a href="#section-2-1" class="level-2">01. 配置代理</a></li>
                    <li><a href="#section-2-2" class="level-2">02. 模型选择与配置</a></li>
                    <li><a href="#section-2-3" class="level-2">03. 语言设置</a></li>
                    <li><a href="#section-2-4" class="level-2">04. 检索文件设置</a></li>
                    <li><a href="#section-2-5" class="level-2">05. Checkpoints配置与管理</a></li>

                    <li><a href="#section-3" class="level-1">三、功能使用指南</a></li>
                    <li><a href="#section-3-1" class="level-2">01. 工程导入</a></li>
                    <li><a href="#section-3-2" class="level-2">02. 自动驾驶模式</a></li>
                    <li><a href="#section-3-3" class="level-2">03. 上下文管理</a></li>
                    <li><a href="#section-3-4" class="level-2">04. 代码辅助功能</a></li>
                    <li><a href="#section-3-5" class="level-2">05. TDD开发工作流</a></li>
                    <li><a href="#section-3-6" class="level-2">06. 代码库rules规则文件使用</a></li>

                    <li><a href="#section-4" class="level-1">四、界面与布局</a></li>
                    <li><a href="#section-4-1" class="level-2">01. 推荐布局方案</a></li>
                    <li><a href="#section-4-2" class="level-2">02. 工作区配置</a></li>
                    <li><a href="#section-4-3" class="level-2">03. 快捷键设置</a></li>

                    <li><a href="#section-5" class="level-1">五、进阶使用</a></li>
                    <li><a href="#section-5-1" class="level-2">01. 输入格式优化建议</a></li>
                    <li><a href="#section-5-2" class="level-2">02. 与版本控制系统配合</a></li>
                    <li><a href="#section-5-3" class="level-2">03. MCP的接入使用</a></li>
                    <li><a href="#section-5-4" class="level-2">04. 代码骨架文档生成</a></li>

                    <li><a href="#section-6" class="level-1">六、附录</a></li>
                    <li><a href="#section-6-1" class="level-2">01. 支持的编程语言</a></li>
                    <li><a href="#section-6-2" class="level-2">02. 推荐的扩展包</a></li>
                    <li><a href="#section-6-3" class="level-2">03. 常见问题与解答</a></li>
                    <li><a href="#section-6-4" class="level-2">04. 问题反馈</a></li>
                </ul>
            </div>

            <div class="content">
                <div class="section" id="section-1">
                    <h2>一、安装配置</h2>

                    <div id="section-1-1">
                        <h3>01. 前置条件</h3>
                        <p>在开始安装之前，请确保您的系统满足以下基本要求：</p>

                        <div class="feature-grid">
                            <div class="feature-card">
                                <h4>VSCode版本</h4>
                                <p>
                                    1.93.0以上<br />
                                    官方地址：<a target="_blank"
                                        href="https://code.visualstudio.com/Download">https://code.visualstudio.com/Download</a><br />
                                    linux系统下载rpm，命令sudo rpm -i 文件名.rpm<br />
                                    windows下载对应的版本即可
                                </p>
                                <p>
                                    vscode相关问题总结（如第1步安装过程有问题可以参考，也可以在群里咨询）：<a target="_blank"
                                        href="https://i.zte.com.cn/index/ispace/#/space/7da9c62f6d5d4e8483b9382729b0479f/wiki/page/fcfbd56e03f5420d97d437cf071e3fdb/view">VSCode的使用</a>
                                </p>
                                <p>
                                    已安装公司最新版本插件精灵1.2.22（插件精灵下载地址<a target="_blank"
                                        href="https://iplug.zx.zte.com.cn/quick-start">https://iplug.zx.zte.com.cn/quick-start</a>）
                                </p>
                            </div>
                            <!-- <div class="feature-card">
                                <h4>运行环境</h4>
                                <p>Node.js 16.0+ 和 npm 8.0+</p>
                            </div>
                            <div class="feature-card">
                                <h4>内存要求</h4>
                                <p>建议至少 8GB RAM，推荐 16GB</p>
                            </div> -->
                        </div>

                        <div class="tip-box">
                            <strong>提示：</strong>建议在安装前关闭所有正在运行的开发环境，以避免端口冲突。
                        </div>
                    </div>

                    <div id="section-1-2">
                        <h3>02. 安装步骤</h3>
                        <div class="step-list">
                            <div class="step-item">
                                <div class="step-number">1</div>
                                <div>
                                    <h4>打开"插件精灵"，输入"零号员工"</h4>
                                    <p></p>
                                </div>
                            </div>
                            <div class="step-item">
                                <div class="step-number">2</div>
                                <div>
                                    <h4>点击"安装"后，重启vscode，侧边栏出现"零号员工"图标即安装成功。</h4>
                                    <img src="./images/1.2/1.2.1.png" />
                                    
                                    </p>
                                </div>
                            </div>
                            <p><b>本地安装</b>：零号员工离线最新版本:1.0.0，<b>安装方式</b>：下载后通过vscode插件进行中直接按安装。</p>
                        </div>
                    </div>

                    <div id="section-1-3">
                        <h3>03. 插件自动更新设置</h3>
                        <p>设置零号员工为自动更新，使可以自动升级到最新版本，使用新版本的特性；设置方式如下：</p>
                        <img src="./images/1.3/1.3.1.png" />
                        <!-- <div class="code-block">
{
  "autoUpdate": {
    "enabled": true,
    "checkInterval": "daily",
    "includePrerelease": false
  }
}
                        </div> -->
                    </div>

                    <div id="section-1-4">
                        <h3>04. Remote模式安装说明</h3>
                        <p>Remote模式允许您在远程服务器上运行开发环境，适合团队协作和资源共享。</p>
                        <div class="warning-box">
                            <strong>注意：</strong>Remote模式需要稳定的网络连接和适当的安全配置。
                        </div>
                        <a target="_blank"
                            href="https://i.zte.com.cn/index/ispace/#/space/644fb45d2b9a4dd7b9971385d6bf1058/wiki/page/ab72f60df78042d2b4052616310b909d/view">Remote模式使用零号员工</a>
                    </div>
                </div>

                <div class="section" id="section-2">
                    <h2>二、基础配置</h2>

                    <div id="section-2-1">
                        <h3>01. 配置代理</h3>
                        <p>需要配置<b
                                style="color: red;">不使用代理</b>选项（重要！！！），通义千问模型和星云模型使用的内部地址，需要将<b>zdsp.zx.zte.com.cn</b>和<b
                                style="background-color: lightgreen;">10.55.57.164</b>在<b>操作系统</b>中设置为不使用代理（如果在vscode设置noproxy会导致公司的统一认证插件出错，从而导致AI开发助手不可用）。
                        </p>

                        <div class="feature-grid">
                            <div class="feature-card">
                                <h4>windows参考</h4>
                                <p>
                                    1）此电脑-属性-高级系统设置-环境变量-新建系统变量<br />
                                    2）变量名：NO_PROXY<br />
                                    3）变量值：zdsp.zx.zte.com.cn,10.55.57.164<br />
                                    4）重启云桌面
                                </p>
                            </div>
                            <div class="feature-card">
                                <h4>linux参考</h4>
                                <p>
                                    <img height="200" src="./images/2.1/2.1.1.png" /><br />
                                    详细资料：<a target="_blank" href="https://izpc.zte.com.cn/community/app/posts/detail?pid=15442"
                                        style="word-break: break-all;">https://izpc.zte.com.cn/community/app/posts/detail?pid=15442</a>
                                </p>
                            </div>
                            <!-- <div class="feature-card">
                                <h4>企业模型</h4>
                                <p>提供高级安全性和定制化功能</p>
                            </div> -->
                        </div>
                    </div>

                    <div id="section-2-2">
                        <h3>02. 模型选择与配置</h3>
                        <p>点击右上角“齿轮”图标进入设置。</p>
                        <h4>可选模型</h4>
                        <p>一共有5个模型可选：星云v6.0-快速反馈、星云v6.0-思维链、千问模型、DeepSeek、openai兼容模型</p>
                        <h4>模型配置</h4>
                        <p>
                            默认已经选择星云v6.0-快速反馈，新安装完插件直接使用时用的就是该模型；千问模型和星云模型使用默认配置即可，后续如果有配置变更会通知大家修改<br />
                            <span
                                style="color: red;">注意：千问模型和星云模型是我司部署的模型可以任意使用，如果配置DeepSeek或者其他外部模型，需要遵照公司信息安全，备案后才能使用。</span><br />
                            如图：<br />
                            <img src="./images/2.2/2.2.1.png" />
                        </p>
                        <h4>计划模式和执行模式</h4>
                        <p>
                            计划模式和执行模式可以配置不同的模型，如需使用相同的模型，可以取消勾选“在计划模式和执行模式中使用不同的模型”<br />
                            <img src="./images/2.2/2.2.2.png" /><br />
                            <img src="./images/2.2/2.2.3.png" />
                        </p>
                        <h4>配置openai兼容模型</h4>
                        <p>
                            配置openai兼容模型时，可以根据你使用的模型所具备的能力，进行如图配置。<br />
                            <img src="./images/2.2/2.2.4.png" />
                        </p>
                    </div>

                    <div id="section-2-3">
                        <h3>03. 语言设置</h3>
                        <p>插件默认中文，可以通过设置修改语言<br /><img src="./images/2.3/2.3.1.png" /></p>
                    </div>

                    <div id="section-2-4">
                        <h3>04. 检索文件设置</h3>
                        <p>
                            选文件，默认不加载隐藏目录，以及部分工程目录，可通过设置界面自行配置<br />
                            <img src="./images/2.4/2.4.1.png" />
                        </p>
                    </div>

                    <div id="section-2-5">
                        <h3>05. Checkpoints配置与管理</h3>
                        <p>
                            <b>功能介绍</b>：checkpoints（检查点）通过建立阶段性任务快照，支持在文件处理或任务执行异常时进行状态回溯。<br />
                            <b>注意</b>：该功能会因保存历史版本而显著增加存储占用，用户可通过"设置"菜单搜索"checkpoint"选项进行开关管理。<br />
                            <b>使用建议</b>：系统默认关闭此功能，建议文件量级超过1000项的中大型项目保持关闭状态以优化存储资源使用效率。<br />
                            <img src="./images/2.5/2.5.1.png" />
                        </p>
                    </div>
                </div>

                <div class="section" id="section-3">
                    <h2>三、功能使用指南</h2>

                    <div id="section-3-1">
                        <h3>01. 工程导入</h3>
                        <p>File->选择目录(需要注意，linux系统有时候不会显示子目录，需要在路径栏末尾输入斜杠'/')</p>
                        <p><b style="color: red;">需要注意</b></p>
                        <p>
                            <li>需要注意排除的目录：’node_modules’, ‘.git’,’.angular’,
                                ‘target’,’classes’,’dist’,’out’,’bundle’,’vendor’,’tmp’,’temp’,’deps’,’pkg’,’Pods’</li>
                            <li>导入的工程不宜过多，根目录过大（排除目录后超过1w文件）会影响生成效果和速度。</li>
                            <li>导入的工程目录尽量用常规方法，不要目录拖动，因为拖动会导致出现多”根目录”的场景（出现UNTITLED），如图：</li>
                        </p>
                        <img height="200" src="./images/3.1/3.1.1.png" />
                        <img height="200" src="./images/3.1/3.1.2.png" />
                    </div>

                    <div id="section-3-2">
                        <h3>02. 自动驾驶模式</h3>
                        <p>默认是开启的自动驾驶模式，零号员工会根据任务编排，自动的创建文件、执行命令等；</p>
                        <img height="200" src="./images/3.2/3.2.1.png" />
                    </div>

                    <div id="section-3-3">
                        <h3>03. 上下文管理</h3>
                        <h4>选择文件/文件夹/Git提交记录/命令/问题/粘贴URL以获取内容</h4>
                        <br>可以通过@的方式选择文件/文件夹/Git提交记录，把当前任务锚定到特定文件/文件夹/Git提交记录等；<br>也可以选择命令、问题或url链接，则根据当前环境自动获取对应文本内容<br>具体各选项功能如下：<br>@添加文件：自动弹出当前工程下文件列表，按需选择，可进一步直接手动输入文件名称筛选。<br>@添加文件夹：同上，自动弹出当前工程下文件夹列表，按需选择，可进一步手动输入文件夹名称筛选。<br>@Git</a>提交记录：自动弹出最近的git提交记录，按需选择。<br>@命令：将VS当前命令行的会话结果自动作为文本内容。<br>@问题：将VS对当前工程中的问题自动作为文本内容。<br>@粘贴URL以获取内容（此功能可能暂时受限无法使用）：粘贴URL
                        ，获取并转换文本内容。</p>
                        <p>以@文件选项为例，示例如下</p>
                        <img height="200" src="./images/3.3/3.3.1.png" />
                        <img height="200" src="./images/3.3/3.3.2.png" />
                        <h4>一键添加上下文功能</h4>
                        <p>选中文件内容，点击右键，选择“添加文件内容至零号员工”，会把选择的内容添加到任务输入框，方便了操作</p>
                        <img height="200" src="./images/3.3/3.3.3.png" />
                    </div>

                    <div id="section-3-4">
                        <h3>04. 代码辅助功能</h3>
                        <h4>快速修复功能</h4>
                        <p>当问题窗口列出有问题时，可单击问题前面的红X图标，选择”使用零号员工修复”选项，进行修复尝试。</p>
                        <p>操作方式和修复结果见下</p>
                        <img height="200" src="./images/3.4/3.4.1.png" />
                        <img height="200" src="./images/3.4/3.4.2.png" />
                    </div>

                    <div id="section-3-5">
                        <h3>05. TDD开发工作流</h3>
                        <p><a target="_blank"
                                href="https://i.zte.com.cn/#/shared/644fb45d2b9a4dd7b9971385d6bf1058/wiki/page/1ff40001f192457c991ee629f7b60292/view">使用说明链接</a>
                        </p>
                    </div>

                    <div id="section-3-6">
                        <h3>06. 代码库rules规则文件使用</h3>
                        <p>零号员工支持基于项目空间的个性化开发规则配置。用户可在VSCode工作区的根目录创建.zerorules文件夹，零号员工则会在执行任务前自动执行以下操作：</p>
                        <ol>
                            <li>深度扫描.zerorules目录下的所有配置文件</li>
                            <li>智能解析文件中的定制化规则和特殊要求</li>
                            <li>将解析内容动态注入系统指令集</li>
                            <li>基于当前项目的上下文生成针对性开发策略<br>这项创新机制赋予用户两大核心优势：<br>•
                                项目级规则继承：通过版本控制系统同步.zerorules配置，确保团队开发规范的一致性<br>• 自适应：根据项目自动匹配对应规则集，实现多项目管理的智能切换</li>
                        </ol>
                        <h4>创建全局 & 项目规则文件</h4>
                        <img height="200" src="./images/3.6/3.6.1.png" />
                        <h4>规则文件建议格式</h4>
                        <p><a target="_blank"
                                href="https://i.zte.com.cn/index/ispace/#/shared/644fb45d2b9a4dd7b9971385d6bf1058/wiki/page/9717df091e6e4784846313902288e270/view">规则文件示例</a>
                        </p>
                        <h4>使用或修改规则文件</h4>
                        <img height="200" src="./images/3.6/3.6.2.png" />
                    </div>
                </div>

                <div class="section" id="section-4">
                    <h2>四、界面与布局</h2>

                    <div id="section-4-1">
                        <h3>01. 推荐布局方案</h3>
                        <p>
                            建议是把插件拖动到<b>右边栏</b>，这样形成3个区域，即：workspace、编辑区、零号员工<br />
                            <img src="./images/4.1/4.1.1.png" /><br />
                            操作方法：鼠标左键拖动左侧插件栏中的零号员工图标，然后拖动到右边区域即可。
                        </p>
                    </div>

                    <div id="section-4-2">
                        <h3>02. 工作区配置</h3>
                        <p>点击 工具栏右上角 “切换辅助侧栏”，可以将零号员工从右侧工作区调出。<br /><img src="./images/4.2/4.2.1.png" /></p>
                    </div>

                    <div id="section-4-3">
                        <h3>03. 快捷键设置</h3>
                        <p>crtl+shift+p，输入零号员工，下拉列表中选择"零号员工：Focus on View"，回车即可，如图：<img src="./images/4.3/4.3.1.png" />
                        </p>
                    </div>
                </div>

                <div class="section" id="section-5">
                    <h2>五、进阶使用</h2>

                    <div id="section-5-1">
                        <h3>01. 输入格式优化建议</h3>
                        <p>掌握最佳的提示词格式，获得更准确的AI响应。</p>

                        <div class="tip-box">
                            <strong>任务描述建议：</strong>使用Markdown方式结构化描述任务需求，如果希望以TDD(测试驱动开发)的方式生成代码，需要明确指示出来。
                        </div>

                        <div class="code-block" onclick="toggleCodeBlock(this)">
                            <h4 style="color: #e2e8f0; margin-bottom: 10px; cursor: pointer;">输入示例</h4>
                            <div class="code-content" style="display: none;">
                                <pre style="margin: 0; white-space: pre-wrap;">
# 角色
你是一个资深的软件开发专家，熟悉go语言技术。
根据输入信息中的**方案设计**，**用户故事**和**文本测试用例**生成生成业务代码.
本次只要求实现'版本包上架功能'

# 输入信息
## 方案设计
### 业务流程和业务规则
    ```
@startuml
actor 运维人员 as Operator
participant "前端界面" as UI
participant "VersionPackageApplication" as AppService
participant "VersionPackageService" as DomainService
participant "PVC存储" as Storage
participant "数据库" as Database
Operator -> UI: 选择版本包文件
Operator -> UI: 点击上传按钮
UI -> AppService: 上传文件(zipPackage)
AppService -> DomainService: publishVersionPackage(zipPackage)

group 文件校验
    DomainService -> DomainService: 校验文件格式
    DomainService -> DomainService: 解析meta.json
    DomainService -> DomainService: 计算SHA-256
end

DomainService -> Storage: 存储文件(humanoid/Vxx.yy.zzz.zip)
DomainService -> Database: 保存元数据(uuid,version,compatibleModels等)
Database --> DomainService: 保存结果
DomainService --> AppService: 返回操作结果
AppService --> UI: 显示上架成功/失败
UI --> Operator: 展示操作结果

@enduml
    ```
### 实现流程：
1、新增“本体软件上架”，
1.4、接口定义
1.4.1 本体软件上架接口
|     |     |
| --- | --- |
| |     |     |
| --- | --- |
| {<br>"swagger": "2.0",<br>"info": {<br>    "version": "1.0.0",<br>    "title": "版本上架API",<br>    "description": "机器人本体软件版本上架接口"<br>},<br>"host": "api.example.com",<br>"basePath": "/api/ability-store/v1",<br>"schemes": \["https"\],<br>"consumes": \["multipart/form-data"\],<br>"produces": \["application/json"\],<br>"paths": {<br>    "/publishVersionPackage": {<br>      "post": {<br>        "summary": "上架新版本",<br>        "description": "上传并校验机器人本体软件版本包",<br>        "operationId": "publishVersionPackage",<br>        "tags": \["版本管理"\],<br>        "security": \[{<br>          "Bearer": \[\]<br>        }\],<br>        "parameters": \[{<br>          "name": "file",<br>          "in": "formData",<br>          "description": "版本包文件(zip格式)",<br>          "required": true,<br>          "type": "file"<br>        }\],<br>        "responses": {<br>          "200": {<br>            "description": "上架成功",<br>            "schema": {<br>              "$ref": "#/definitions/VersionPackage"<br>            }<br>          },<br>          "400": {<br>            "description": "文件结构不完整",<br>            "schema": {<br>              "$ref": "#/definitions/ErrorResponse"<br>            }<br>          },<br>          "409": {<br>            "description": "版本冲突",<br>            "schema": {<br>              "$ref": "#/definitions/ErrorResponse"<br>            }<br>          },<br>          "500": {<br>            "description": "服务器内部错误",<br>            "schema": {<br>              "$ref": "#/definitions/ErrorResponse"<br>            }<br>          }<br>        }<br>      }<br>    }<br>},<br>"definitions": {<br>    "VersionPackage": {<br>      "type": "object",<br>      "properties": {<br>        "uuid": {<br>          "type": "string",<br>          "example": "e2f12a28-53d9-4a5f-8800-03cd96aaf2aa"<br>        },<br>        "type": {<br>          "type": "string",<br>          "example": "humanoid"<br>        },<br>        "version": {<br>          "type": "string",<br>          "example": "V01.02.003"<br>        },<br>        "compatibleModels": {<br>          "type": "array",<br>          "items": {<br>            "type": "string"<br>          },<br>          "example": \["modelA", "modelB"\]<br>        },<br>        "storagePath": {<br>          "type": "string",<br>          "example": "humanoid/V01.02.003.zip"<br>        },<br>        "status": {<br>          "type": "string",<br>          "enum": \["PUBLISHED", "UNPUBLISHED"\],<br>          "example": "PUBLISHED"<br>        }<br>      }<br>    },<br>    "ErrorResponse": {<br>      "type": "object",<br>      "properties": {<br>        "code": {<br>          "type": "integer",<br>          "example": 400<br>        },<br>        "message": {<br>          "type": "string",<br>          "example": "FILE\_STRUCTURE\_INVALID"<br>        },<br>        "details": {<br>          "type": "string",<br>          "example": "文件缺少meta.json描述文件"<br>        }<br>      }<br>    }<br>},<br>"securityDefinitions": {<br>    "Bearer": {<br>      "type": "apiKey",<br>      "name": "Authorization",<br>      "in": "header"<br>    }<br>}<br>} |  | |  |
## 用户故事
用户故事列表
### 用户故事2
作为运维人员
我希望上传本体软件包并自动完成完整性校验和存储，以便将已验证的版本包上架到仓库中供后续使用。

## 文本测试用例
功能: 机器人软件版本管理
    作为运维人员
    我希望能够管理机器人软件版本
    以便实现版本的全生命周期管理
    Scenario Outline: 上传有效版本包
    Given 运维人员选择<文件大小>的有效版本包文件
    When 运维人员点击上传按钮并提交文件
    Then 文件被完整保存到PVC
    And 数据库状态更新为"已上架"
    Examples:
        | 文件大小 |
        | 50G     |
        | 99G     |
    Scenario: 上传校验失败的版本包
    Given 运维人员提交损坏的版本包文件
    When 系统进行完整性校验
    Then 系统提示"文件校验失败"
    And 拒绝保存文件
    Scenario: 上传超大文件
    Given 运维人员尝试提交超过100G的文件
    When 系统检查文件大小
    Then 系统立即拦截并提示"文件超出最大限制"
    # 补充的边界测试场景
    Scenario: 上传格式错误的文件名
    Given 运维人员提交文件名格式错误的版本包
    When 系统进行文件名校验
    Then 系统提示"文件名格式不符合规范"
    And 拒绝保存文件
# 实现说明
这是一个洋葱模型架构，基于现在这个已有的DDD框架代码，用go语言实现用户故事

# 任务要求
实现用户故事，需要覆盖所有用例，代码需要从ui层到 → 应用服务 → 领域服务/仓储 → 基础设施层全部贯通。
按照UML中的流程实现
                                </pre>
                            </div>
                        </div>
                    </div>

                    <div id="section-5-2">
                        <h3>02. 与版本控制系统配合</h3>
                        <p>深度集成Git等版本控制系统，实现无缝的版本管理。</p>
                        <div class="tip-box">
                            <strong>前置条件：</strong>勾选checkpoints选项
                        </div>

                        <div class="img-code-block" onclick="toggleCodeBlock(this)">
                            <h4 style="color: #27292b; margin-bottom: 10px; cursor: pointer;">使用方法</h4>
                            <div class="code-content" style="display: none;">
                                <pre style="margin: 0; white-space: pre-wrap;">
在每次文件变动时，会生成一个检查点，用于历史文件状态的回溯。点击“还原”按钮，会弹出三个选项：
<li>还原文件：将项目文件还原为此时的快照</li>
<li>恢复任务：不改变文件状态，将任务还原到该检查点的状态</li>
<li>恢复文件和任务：将文件和任务都还原至该检查点的状态</li>
<img src="./images/5.2/5.2.1.png" />
                                </pre>
                            </div>
                        </div>
                    </div>

                    <div id="section-5-3">
                        <h3>03. MCP的接入使用</h3>
                        <p>配置和使用Model Context Protocol，扩展AI能力。</p>

                        <div class="tip-box">
                            <strong>前置条件：</strong>
                            <li>在vscode中配置代理，否则无法访问MCP Marketplace等</li>
                            <li>需要根据具体MCP Server的要求，本地可能需要具备npx/uv/python等相关环境</li>
                        </div>

                        <div class="img-code-block" onclick="toggleCodeBlock(this)">
                            <h4 style="color: #27292b; margin-bottom: 10px; cursor: pointer;">配置示例</h4>
                            <div class="code-content" style="display: none;">
                                <pre style="margin: 0; white-space: pre-wrap;">
                                    <img src="./images/5.3/5.3.1.png" />
                                </pre>
                            </div>
                        </div>

                        <div class="img-code-block" onclick="toggleCodeBlock(this)">
                            <h4 style="color: #27292b; margin-bottom: 10px; cursor: pointer;">添加MCP Server示例</h4>
                            <div class="code-content" style="display: none;">
                                <pre style="margin: 0; white-space: pre-wrap;">
方法一: 通过MCP Marketplace下载        
点击install，零号员工会创建对应的MCP Server的安装任务，等待任务执行完即可                            
                                    <div style="display: flex; gap: 20px;">
                                        <img src="./images/5.3/5.3.2.png" />
                                        <img src="./images/5.3/5.3.3.png" />
                                    </div>
方法二：对接远程服务器
可以对接公司提供的chat-with-search工具
<img src="./images/5.3/5.3.4.png" />

方法三: 使用本地MCP Server
在“已安装”页面，点击“配置MCP服务器”，在弹出的配置文件中，按照本地MCP Server的要求，完成相关配置
<img src="./images/5.3/5.3.5.png" />
                                </pre>
                            </div>
                        </div>

                        <div class="img-code-block" onclick="toggleCodeBlock(this)">
                            <h4 style="color: #27292b; margin-bottom: 10px; cursor: pointer;">使用MCP Server示例</h4>
                            <div class="code-content" style="display: none;">
                                <pre style="margin: 0; white-space: pre-wrap;">

<img src="./images/5.3/5.3.6.png" />
                                </pre>
                            </div>
                        </div>
                    </div>

                    <div id="section-5-4">
                        <h3>04. 代码骨架文档生成</h3>
                        <p>自动生成项目文档和代码结构说明。</p>
                        <div class="img-code-block" onclick="toggleCodeBlock(this)">
                            <h4 style="color: #27292b; margin-bottom: 10px; cursor: pointer;">代码骨架文档生成</h4>
                            <div class="code-content" style="display: none;">
                                <div class="section">
                                    <h2>​文档生成功能使用指南</h2>
                                    <div class="markdown-body" data-markdown>
## 概述

文档生成功能允许您快速为项目创建标准化的文档结构。通过简单的右键菜单操作，您可以生成包括README、架构设计、API文档等在内的多种文档模板，帮助您更好地记录和管理项目知识。

## 功能特点
- 一键生成多种文档模板
- 自动跳过已存在的文件，避免覆盖
- 支持自定义文档模板内容
- 提供默认选中的核心文档模板
- 防止在docs目录中嵌套创建文档

## 基本使用

### 生成文档

1. 在VS Code的资源管理器中，右键点击目标文件夹或文件
2. 在上下文菜单中选择"Generate Documentation Skeleton / 生成文档骨架"
3. 在弹出的选择框中，勾选您需要生成的文档（默认已选择推荐文档）
4. 点击确定，系统将在目标目录中生成所选文档

> **注意**：如果您选择的是文件而非文件夹，系统会在该文件所在的目录中生成文档。

### 默认选中的文档

以下文档默认被选中（如果它们尚不存在）：
- README.md - 项目/模块整体介绍
- docs/architecture.md - 架构设计文档
- docs/api/api_overview.md - API概览文档
- docs/tutorials/getting_started.md - 入门指南
- docs/glossary.md - 术语表
- docs/development.md - 开发指南

这些文档基于[代码业务知识显式化方法](https://i.zte.com.cn/#/shared/db2bc11e40324859bde032b212536fd5/wiki/page/d4a0ea7b0487496d99291ca44f42229e/view)中的建议，涵盖了项目文档的核心内容。

### 已存在文件的处理

当您尝试生成的文档已经存在时：
- 系统会显示通知，告知您哪些文件已经存在
- 已存在的文件不会出现在选择列表中
- 系统只会生成尚不存在的文档

### 目录限制

为避免创建嵌套的文档结构，当您尝试在"docs"目录或其子目录中生成文档时，系统会自动将目标目录调整为"docs"目录的父目录。

例如：
- 如果您在 `/project/docs` 目录中右键选择生成文档，实际生成位置将是 `/project`
- 如果您在 `/project/docs/api` 目录中右键选择生成文档，实际生成位置也将是 `/project`

这样可以确保文档始终生成在正确的位置，避免创建嵌套的文档结构。

## 自定义文档模板

您可以根据自己的需求自定义文档模板内容。有两种方式可以实现这一点：

### 方法一：使用模板编辑器命令（推荐）
1. **编辑现有模板**：
- 打开命令面板（按 `Ctrl+Shift+P` 或 `Cmd+Shift+P`）
- 输入 `Edit Document Template` 并选择命令
- 从列表中选择要编辑的模板
- 在打开的编辑器中编辑模板内容
- 保存并关闭编辑器，系统会自动将内容更新到设置中

2. **创建新模板**：
- 打开命令面板（按 `Ctrl+Shift+P` 或 `Cmd+Shift+P`）
- 输入 `Create New Document Template` 并选择命令
- 输入新模板的文件路径（例如 `docs/custom.md`）
- 在打开的编辑器中编写模板内容
- 保存并关闭编辑器，系统会自动将内容添加到设置中

### 方法二：直接编辑settings.json
1. 打开VS Code设置（文件 > 首选项 > 设置）
2. 点击右上角的"打开设置(JSON)"图标
3. 添加或修改 `employeeZero.docgen.templates` 配置

示例配置：
```json
"employeeZero.docgen.templates": {
"README.md": "# 我的项目\n\n这是一个自定义的README模板。\n",
"docs/architecture.md": "# 架构设计\n\n这是一个自定义的架构文档模板。\n",
"docs/custom.md": "# 自定义文档\n\n这是一个新增的自定义文档模板。\n"
}
```

> **注意**：此设置只能在 settings.json 文件中编辑，不会在VS Code的图形化设置界面中显示编辑选项。

## 模板语法

模板内容使用Markdown格式，并支持以下特性：
- 标准Markdown语法（标题、列表、表格等）
- 换行符使用`\n`表示
- 可以包含代码块和其他Markdown高级格式

示例：

```json
"README.md": "# 项目名称\n\n## 简介\n\n这是项目简介。\n\n## 功能特点\n\n- 功能1\n- 功能2\n- 功能3\n"
```

## 常见问题
### Q: 如何一次性生成所有文档？
A: 在选择文档的对话框中，您可以手动选择所有未被选中的文档，或者使用快捷键 `Ctrl+A`（Windows/Linux）或 `Cmd+A`（Mac）全选。

### Q: 我可以在生成后修改文档内容吗？
A: 是的，生成的文档是普通的Markdown文件，您可以随时编辑它们。模板只是提供了初始内容。

### Q: 如何添加项目特定的信息到模板中？
A: 您可以自定义模板内容，加入项目特定的信息。目前需要手动编辑模板，你可以使用大模型帮助生成这些内容，但需要人工校验。

### Q: 为什么我看不到"Generate Documentation Skeleton"选项？
A: 请确保您已安装并启用了插件，并且您正在资源管理器视图中右键点击文件或文件夹。

### Q: 如何恢复默认模板？
A: 从settings.json中删除`employeeZero.docgen.templates`配置项，插件将使用默认模板。

## 最佳实践
1. **在项目根目录生成文档**：为了保持标准的项目结构，建议在项目根目录生成文档。对于大型项目，亦可以在子模块目录生成文档，此时README.md中描述的是子模块的文档。
2. **根据项目需求选择文档**：不是所有项目都需要所有类型的文档，根据项目规模和性质选择适当的文档。
3. **定期更新文档**：生成文档后，请确保随着项目发展定期更新文档内容。
4. **自定义团队模板**：为团队创建标准化的文档模板，确保所有项目文档风格一致。
5. **结合代码注释**：文档应与代码注释相辅相成，共同构成完整的项目知识体系。

---
如有任何问题或建议，请通过项目仓库的Issues页面反馈。
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    </div>

                <div class="section" id="section-6">
                    <h2>六、附录</h2>

                    <div id="section-6-1">
                        <h3>01. 支持的编程语言</h3>
                        <div class="feature-grid">
                            <div class="feature-card">
                                <h4>主流语言</h4>
                                <p>JavaScript, Python, Java, C#, C++, Go</p>
                            </div>
                            <div class="feature-card">
                                <h4>Web技术</h4>
                                <p>HTML, TypeScript</p>
                            </div>
                            <div class="feature-card">
                                <h4>其他语言</h4>
                                <p>Rust, Swift, Ruby</p>
                            </div>
                        </div>
                    </div>

                    <div id="section-6-2">
                        <h3>02. 推荐的扩展包</h3>
                        <p>零号员工在代码生成过程中会自动读取workspace的问题区的内容，并自动规划进行修复。建议安装对应开发语言的扩展包，使具备语法自动检查功能<br>
                            可以在插件市场中搜索并安装 :
                        <div class="img-code-block" onclick="toggleCodeBlock(this)">
                            <h4 style="color: #27292b; margin-bottom: 10px; cursor: pointer;">搜索语言扩展包示例</h4>
                            <div class="code-content" style="display: none;">
                                <pre style="margin: 0; white-space: pre-wrap;">
    <img src="./images/6.2/6.2.1.png" />
                                    </pre>
                            </div>
                        </div>
                        </p>
                        <table border="1">
                            <thead>
                                <tr>
                                    <th width="30" align="center">#</th>
                                    <th width="80">语言</th>
                                    <th width="300">扩展包</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td align="center">1</td>
                                    <td>Java</td>
                                    <td>Extension Pack for Java</td>
                                </tr>
                                <tr>
                                    <td align="center">2</td>
                                    <td>C/C++</td>
                                    <td>C/C++ Extension Pack</td>
                                </tr>
                                <tr>
                                    <td align="center">3</td>
                                    <td>Python</td>
                                    <td>Python Extension Pack</td>
                                </tr>
                                <tr>
                                    <td align="center">4</td>
                                    <td>...</td>
                                    <td>...</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <div id="section-6-3">
                        <h3>03. 常见问题与解答</h3>
                        <div class="tip-box">
                            <a target="_blank"
                                href="https://i.zte.com.cn/index/ispace/#/space/644fb45d2b9a4dd7b9971385d6bf1058/wiki/page/818fc4a723de486087bd0b99b71008ae/view">零号员工使用FAQ</a>
                        </div>

                    </div>

                    <div id="section-6-4">
                        <h3>04. 问题反馈</h3>
                        <li> 如果使用过程中有建议或者插件问题，可以登记到此页面: <a target="_blank" href="https://i.zte.com.cn/index/ispace/#/space/644fb45d2b9a4dd7b9971385d6bf1058/wiki/page/ae1f7db5b60b45709e773e34e683915d/view" target="_blank">零号员工问题反馈</a></li>
                         <p> &nbsp; &nbsp; &nbsp; 插件设置界面的底部也有该反馈的入口：</p>
                        <div class="img-code-block" onclick="toggleCodeBlock(this)">
                            <h4 style="color: #27292b; margin-bottom: 10px; cursor: pointer;">插件中的问题反馈入口</h4>
                            <div class="code-content" style="display: none;">
                                <pre style="margin: 0; white-space: pre-wrap;">
    <img src="./images/6.4/6.4.1.png" />
                                    </pre>
                            </div>
                        </div>
                        </p>

                        <p> <li>或在零号员工使用支持沟通群内寻求支持：</li>
                        <div class="code-content">
                            <pre style="margin: 0; white-space: pre-wrap;">
<img src="./images/6.4/6.4.2.png" />
                                </pre>
                        </div>

                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="scroll-to-top" onclick="scrollToTop()">↑</div>

    <script>
        // 平滑滚动功能
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        document.querySelectorAll('[data-markdown]').forEach(elem=>{const raw=elem.textContent.trim(); elem.innerHTML=marked.parse(raw);});

        // 回到顶部按钮
        const scrollToTopBtn = document.querySelector('.scroll-to-top');

        window.addEventListener('scroll', () => {
            if (window.pageYOffset > 300) {
                scrollToTopBtn.classList.add('visible');
            } else {
                scrollToTopBtn.classList.remove('visible');
            }
        });

        function scrollToTop() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        }

        function toggleCodeBlock(element) {
            const content = element.querySelector('.code-content');
            const arrow = element.querySelector('h4');
            if (content.style.display === 'none') {
                content.style.display = 'block';
                arrow.innerHTML = arrow.innerHTML.replace('▼', '▲');
            } else {
                content.style.display = 'none';
                arrow.innerHTML = arrow.innerHTML.replace('▲', '▼');
            }
        }

        // 活跃导航高亮
        const sections = document.querySelectorAll('.section');
        const navLinks = document.querySelectorAll('.toc a');

        window.addEventListener('scroll', () => {
            let current = '';
            sections.forEach(section => {
                const sectionTop = section.offsetTop;
                const sectionHeight = section.clientHeight;
                if (pageYOffset >= sectionTop - 200) {
                    current = section.getAttribute('id');
                }
            });

            navLinks.forEach(link => {
                link.classList.remove('active');
                if (link.getAttribute('href') === '#' + current) {
                    link.classList.add('active');
                }
            });
        });
    </script>

    <style>
        .toc a.active {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            transform: translateX(5px);
        }
    </style>
</body>

</html>